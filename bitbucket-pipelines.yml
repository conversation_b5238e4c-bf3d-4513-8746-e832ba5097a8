clone:
  depth: full
definitions:
  services:
    docker:
      image:
        name: docker:dind
        username: $DOCKER_HUB_USERNAME
        password: $DOCKER_HUB_PASSWORD
        email: $DOCKER_HUB_EMAIL
      memory: 1024
  steps:
    - step: &prep-env
        name: Prepare Environment Vars
        oidc: true
        size: 1x
        image:
          name: amazon/aws-cli:latest
          username: $DOCKER_HUB_USERNAME
          password: $DOCKER_HUB_PASSWORD
          email: $DOCKER_HUB_EMAIL
        runs-on:
          - self.hosted
          - linux
        script:
          # Export env vars from file
          - set -a && . ./pipeline.env && set +a
          # Set AWSCLI env vars
          - export AWS_WEB_IDENTITY_TOKEN_FILE=$(pwd)/web-identity-token
          - echo $BITBUCKET_STEP_OIDC_TOKEN > $(pwd)/web-identity-token
          # Create Canonical Version
          - export timestamp=$(date +%s | tr -d '[:space:]')
          - export escaped_branch=$(echo $BITBUCKET_<PERSON>ANCH | sed "s=/=_=g" | tr -d '[:space:]')
          - echo export BRANCH_NAME=$escaped_branch >> build.env
          - echo export BUILD_IMAGE_TAG="${escaped_branch}-${BITBUCKET_COMMIT:0:8}-${timestamp}" >> build.env
          # Find base branch for use in image building
          - if [[ $escaped_branch == $STG_BRANCH ]]; then echo export BASE_BRANCH=$STG_BRANCH >> build.env; elif [[ $escaped_branch == $PRD_BRANCH ]]; then echo export BASE_BRANCH=$PRD_BRANCH >> build.env; else echo export BASE_BRANCH=$DEV_BRANCH >> build.env; fi
          # Get Snyk Creds
          - echo export SNYK_TOKEN=$(aws secretsmanager get-secret-value --secret-id devTools/snyk_token --query SecretString --output text) >> build.env
          # Get AWS Creds
          - echo export AWS_TOKEN=$(aws ecr get-login-password --region us-east-1) >> build.env
          # Get ACR Creds
          - echo export ACR_DV_USER=$(aws secretsmanager get-secret-value --secret-id $DV_SECRET --query SecretString --output text | grep -Eo '"appId"[^,}]*' | grep -Eo '[^:]*$') >> build.env
          - echo export ACR_DV_PASS=$(aws secretsmanager get-secret-value --secret-id $DV_SECRET --query SecretString --output text | grep -Eo '"password"[^,}]*' | grep -Eo '[^:]*$') >> build.env
          - echo export ACR_DV_REPO=$(aws secretsmanager get-secret-value --secret-id $DV_SECRET --query SecretString --output text | grep -Eo '"repository"[^,}]*' | grep -Eo '[^:]*$') >> build.env
          - echo export ACR_UA_USER=$(aws secretsmanager get-secret-value --secret-id $UA_SECRET --query SecretString --output text | grep -Eo '"appId"[^,}]*' | grep -Eo '[^:]*$') >> build.env
          - echo export ACR_UA_PASS=$(aws secretsmanager get-secret-value --secret-id $UA_SECRET --query SecretString --output text | grep -Eo '"password"[^,}]*' | grep -Eo '[^:]*$') >> build.env
          - echo export ACR_UA_REPO=$(aws secretsmanager get-secret-value --secret-id $UA_SECRET --query SecretString --output text | grep -Eo '"repository"[^,}]*' | grep -Eo '[^:]*$') >> build.env
          # Code Artifact Creds
          - echo export CODEARTIFACT_AUTH_TOKEN=$(aws codeartifact get-authorization-token --domain esource-int-artifacts --domain-owner ************ --region us-east-1 --query authorizationToken --output text) >> build.env
          - echo export CODEARTIFACT_ENDPOINT=$(aws codeartifact get-repository-endpoint --domain esource-int-artifacts --domain-owner ************ --repository pypi --format pypi --query repositoryEndpoint --output text) >> build.env
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        artifacts:
          - build.env
    - step: &sonarqube
        name: SonarQube Quality Gate
        oidc: true
        size: 1x
        image:
          name: sonarsource/sonar-scanner-cli:11.1
          username: $DOCKER_HUB_USERNAME
          password: $DOCKER_HUB_PASSWORD
          email: $DOCKER_HUB_EMAIL
        runs-on:
          - self.hosted
          - linux
        script:
          - set -a && . pipeline.env && . build.env && set +a
          - sonar-scanner
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
    - step: &build-image
        name: Build Image
        oidc: true
        size: 1x
        image:
          name: docker:20
          username: $DOCKER_HUB_USERNAME
          password: $DOCKER_HUB_PASSWORD
          email: $DOCKER_HUB_EMAIL
        runs-on:
          - self.hosted
          - linux
        script:
          - set -a && . pipeline.env && . build.env && set +a
          - echo $AWS_TOKEN | docker login -u AWS --password-stdin $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME
          - docker context create my-builder
          - docker buildx create my-builder --driver docker-container --use
          - docker buildx build -f Dockerfile -t $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG --progress plain --output type=docker,dest=built.tar --cache-to type=registry,ref=$BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:buildcache,mode=max,oci-mediatypes=true,image-manifest=true --cache-from type=registry,ref=$BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:buildcache .
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        artifacts:
          - built.tar
        services:
          - docker
    - step: &snyk-image
        name: Synk - Image Test
        oidc: true
        size: 1x
        runs-on:
          - self.hosted
          - linux
        image:
          name: snyk/snyk:docker
          username: $DOCKER_HUB_USERNAME
          password: $DOCKER_HUB_PASSWORD
          email: $DOCKER_HUB_EMAIL
        script:
          - set -a && . pipeline.env && . build.env && set +a
          - snyk container test docker-archive:built.tar --policy-path=$BITBUCKET_CLONE_DIR/policy.snyk --severity-threshold=critical --print-deps --color --json-file-output=output.json
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        artifacts:
          - output.json
    - step: &upload-snyk-image-report
        name: Synk - Upload Image Test Report
        oidc: true
        size: 1x
        runs-on:
          - self.hosted
          - linux
        image:
          name: bitbucketpipelines/bitbucket-upload-file:0.7.3
          username: $DOCKER_HUB_USERNAME
          password: $DOCKER_HUB_PASSWORD
          email: $DOCKER_HUB_EMAIL
        script:
          - set -a && . pipeline.env && . build.env && set +a
          - export BITBUCKET_ACCESS_TOKEN=$BITBUCKET_TOKEN
          - export FILENAME=snyk-image-$BUILD_IMAGE_TAG.json
          - cp output.json $FILENAME
          - python3 /pipe.py
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
    - step: &push-ecr
        name: Push to ECR
        oidc: true
        size: 1x
        runs-on:
          - self.hosted
          - linux
        script:
          - set -a && . pipeline.env && . build.env && set +a
          - docker load --input built.tar
          - echo $AWS_TOKEN | docker login -u AWS --password-stdin $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME
          - docker push $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG
          - docker tag $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BRANCH_NAME-latest
          - docker push $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BRANCH_NAME-latest
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        services:
          - docker
    - step: &push-acr-development
        name: Push to ACR - Development
        oidc: true
        size: 1x
        image:
          name: docker:latest
          username: $DOCKER_HUB_USERNAME
          password: $DOCKER_HUB_PASSWORD
          email: $DOCKER_HUB_EMAIL
        runs-on:
          - self.hosted
          - linux
        script:
          - set -a && . pipeline.env && . build.env && set +a
          - docker load --input built.tar
          - echo $ACR_DV_PASS | docker login -u $ACR_DV_USER --password-stdin $ACR_DV_REPO
          - docker tag $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG $ACR_DV_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG
          - docker push $ACR_DV_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG
          - docker tag $ACR_DV_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG $ACR_DV_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:latest
          - docker push $ACR_DV_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:latest
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        services:
          - docker
    - step: &push-acr-staging
        name: Push to ACR - Staging
        oidc: true
        size: 1x
        image:
          name: docker:latest
          username: $DOCKER_HUB_USERNAME
          password: $DOCKER_HUB_PASSWORD
          email: $DOCKER_HUB_EMAIL
        runs-on:
          - self.hosted
          - linux
        script:
          - set -a && . pipeline.env && . build.env && set +a
          - docker load --input built.tar
          - echo $ACR_UA_PASS | docker login -u $ACR_UA_USER --password-stdin $ACR_UA_REPO
          - docker tag $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG $ACR_UA_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG
          - docker push $ACR_UA_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG
          - docker tag $ACR_UA_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG $ACR_UA_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:latest
          - docker push $ACR_UA_REPO/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:latest
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        services:
          - docker
    - step: &finish
        name: Notify Teams of Success
        oidc: true
        size: 1x
        runs-on:
          - self.hosted
          - linux
        script:
          - set -a && . pipeline.env && . build.env && set +a
          - echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin
          - docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Succeeded" "${BITBUCKET_REPO_FULL_NAME} -- ${BITBUCKET_BRANCH} -- $BUILD_TAG"
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        services:
          - docker

pipelines:
  default:
    - step: *prep-env
    - step: *sonarqube
    - step: *finish
  branches:
    '{development}':
      - step: *prep-env
      - step: *sonarqube
      - step: *build-image
      - step: *snyk-image
      - step: *upload-snyk-image-report
      - step: *push-ecr
      - step: *push-acr-development
      - step: *finish
    '{staging}':
      - step: *prep-env
      - step: *sonarqube
      - step: *build-image
      - step: *snyk-image
      - step: *upload-snyk-image-report
      - step: *push-ecr
      - step: *push-acr-staging
      - step: *finish
    '{main}':
      - step: *prep-env
      - step: *sonarqube
      - step: *build-image
      - step: *snyk-image
      - step: *upload-snyk-image-report
      - step: *push-ecr
      - step: *finish