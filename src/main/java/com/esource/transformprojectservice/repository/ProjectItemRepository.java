package com.esource.transformprojectservice.repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.data.rest.core.annotation.RestResource;

import com.esource.transformprojectservice.model.entity.ProjectItem;
import com.esource.transformprojectservice.model.enumeration.Status;

@RepositoryRestResource(collectionResourceRel = "project-items", path = "project-items")
public interface ProjectItemRepository extends MongoRepository<ProjectItem, String> {

    List<ProjectItem> findByProjectId(String projectId);

    @RestResource(exported = false)
    @Aggregation({
        "{'$match': { 'status': {'$in': ?0}}}",
        "{'$group': {'_id': '$projectId'}}",
        "{'$count': 'projectsCount'}"
    })
    Long countProjectIdsByStatusIn(Set<Status> statusSet);

    Optional<ProjectItem> findByIdAndProjectId(String id, String projectId);
}
