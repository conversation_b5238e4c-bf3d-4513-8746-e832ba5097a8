package com.esource.transformprojectservice.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.esource.transformprojectservice.model.dto.response.StageGateDTO;
import com.esource.transformprojectservice.model.entity.Activity;

@Repository
public interface ActivityRepository extends MongoRepository<Activity, String> {

    Optional<Activity> findByProjectItemIdAndActivityId(String projectItemId, String activityId);

    List<Activity> findByProjectItemId(String projectItemId);

    @Aggregation({
        "{'$match': { 'projectItemId': ?0 } }",
        "{'$group': {'_id': '$gate', 'activities': {'$push': '$$ROOT'}}}",
        "{'$project': {'_id': 0, 'gate': '$_id', 'activities': 1}}"
    })
    List<StageGateDTO> getStageGatesForItem(String projectItemId);
}
