package com.esource.transformprojectservice.repository;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;

import com.esource.transformprojectservice.model.entity.AuditLog;

@RepositoryRestResource(collectionResourceRel = "audit-logs", path = "audit-logs")
public interface AuditLogRepository extends MongoRepository<AuditLog, String> {

    Page<AuditLog> findByOrderByTimestampDesc(Pageable pageable);

    Page<AuditLog> findByAssetTypeInOrderByTimestampDesc(List<String> assetTypes, Pageable pageable);

    Page<AuditLog> findByAssetIdInOrderByTimestampDesc(List<String> assetIds, Pageable pageable);

    Page<AuditLog> findByAssetTypeAndAssetIdOrderByTimestampDesc(String assetType, String assetId, Pageable pageable);

    Page<AuditLog> findByAssetTypeInAndAssetIdInOrderByTimestampDesc(
            List<String> assetTypes, List<String> assetIds, Pageable pageable);
}
