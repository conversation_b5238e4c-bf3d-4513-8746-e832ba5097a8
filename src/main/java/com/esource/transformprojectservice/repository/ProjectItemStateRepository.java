package com.esource.transformprojectservice.repository;

import java.util.List;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.esource.transformprojectservice.model.entity.ProjectItemState;

@Repository
public interface ProjectItemStateRepository extends MongoRepository<ProjectItemState, String> {

    List<ProjectItemState> findByProjectId(String projectId);
}
