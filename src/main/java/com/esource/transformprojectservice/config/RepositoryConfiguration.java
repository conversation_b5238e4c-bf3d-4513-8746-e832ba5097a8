package com.esource.transformprojectservice.config;

import java.util.List;
import java.util.Optional;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.mongodb.config.EnableMongoAuditing;
import org.springframework.data.mongodb.core.convert.MongoCustomConversions;
import org.springframework.data.rest.core.config.RepositoryRestConfiguration;
import org.springframework.data.rest.webmvc.config.RepositoryRestConfigurer;
import org.springframework.web.servlet.config.annotation.CorsRegistry;

import com.esource.transformprojectservice.config.converters.StatusConverters.StatusToStringConverter;
import com.esource.transformprojectservice.config.converters.StatusConverters.StringToStatusConverter;
import com.esource.transformprojectservice.model.entity.Project;
import com.esource.transformprojectservice.model.entity.ProjectItem;

import static com.esource.security.auth.utils.SecurityUtils.getCurrentUserEmail;
import static com.esource.security.auth.utils.SecurityUtils.isUserLoggedIn;

@Configuration
@EnableMongoAuditing(auditorAwareRef = "auditorAware")
public class RepositoryConfiguration implements RepositoryRestConfigurer {

    /**
     * Gives Mongo Auditing the ability to resolve the current user. This allows it to populate @CreatedBy and @LastModifiedBy fields.
     *
     * @return The auditor aware bean
     */
    @Bean(name = "auditorAware")
    public AuditorAware<String> auditor() {
        return () -> Optional.of(isUserLoggedIn() ? getCurrentUserEmail() : "Unknown User");
    }

    @Override
    public void configureRepositoryRestConfiguration(RepositoryRestConfiguration config, CorsRegistry cors) {
        config.exposeIdsFor(Project.class, ProjectItem.class);
    }

    @Bean
    public MongoCustomConversions mongoCustomConversions() {
        return new MongoCustomConversions(List.of(new StatusToStringConverter(), new StringToStatusConverter()));
    }
}
