package com.esource.transformprojectservice.config;

import java.util.concurrent.TimeUnit;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.github.benmanes.caffeine.cache.Caffeine;

import lombok.NonNull;

@EnableCaching
@Configuration
public class CacheConfig {

    private static final int APPROX_MAX_USERS = 250;
    private static final int APPROX_AVG_USERS = 10;
    private static final int APPROX_FILTER_RETENTION_HOURS = 2;

    @Bean
    public Caffeine<@NonNull Object, @NonNull Object> caffeineConfig() {
        return Caffeine.newBuilder()
                .expireAfterWrite(APPROX_FILTER_RETENTION_HOURS, TimeUnit.HOURS)
                .initialCapacity(APPROX_AVG_USERS)
                .maximumSize(APPROX_MAX_USERS);
    }

    @Bean
    public CacheManager cacheManager(Caffeine<@NonNull Object, @NonNull Object> caffeine) {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(caffeine);
        return cacheManager;
    }
}
