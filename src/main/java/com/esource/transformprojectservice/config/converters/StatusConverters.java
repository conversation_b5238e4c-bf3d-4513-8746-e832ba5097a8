package com.esource.transformprojectservice.config.converters;

import org.springframework.core.convert.converter.Converter;
import org.springframework.data.convert.ReadingConverter;
import org.springframework.data.convert.WritingConverter;

import com.esource.transformprojectservice.model.enumeration.Status;
import com.mongodb.lang.NonNull;

public class StatusConverters {

    @WritingConverter
    public static class StatusToStringConverter implements Converter<Status, String> {

        @Override
        public String convert(@NonNull Status status) {
            return status.getLabel();
        }
    }

    @ReadingConverter
    public static class StringToStatusConverter implements Converter<String, Status> {

        @Override
        public Status convert(@NonNull String status) {
            return Status.of(status);
        }
    }
}
