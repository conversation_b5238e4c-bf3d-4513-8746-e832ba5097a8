package com.esource.transformprojectservice.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.esource.transformprojectservice.model.dto.response.AggregateOverviewDTO;
import com.esource.transformprojectservice.service.AggregateOverviewService;

@RestController
@RequestMapping("/v1/overview")
public class OverviewController {
    @Autowired
    private AggregateOverviewService aggregateOverviewService;

    @GetMapping
    public AggregateOverviewDTO getAggregateOverview() {
        return aggregateOverviewService.generateAggregateOverview();
    }
}
