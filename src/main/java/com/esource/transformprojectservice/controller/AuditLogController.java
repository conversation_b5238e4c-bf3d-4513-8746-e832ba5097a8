package com.esource.transformprojectservice.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.esource.transformprojectservice.model.entity.AuditLog;
import com.esource.transformprojectservice.repository.AuditLogRepository;

@RestController
@RequestMapping("/v1/audit-logs")
public class AuditLogController {

    @Autowired
    private AuditLogRepository auditLogRepository;

    @GetMapping("/{assetType}/assets/{assetId}")
    public PagedModel<AuditLog> getAuditLogsByAssetTypeAndId(
            @PathVariable String assetType, @PathVariable String assetId, Pageable page) {

        return new PagedModel<>(
                auditLogRepository.findByAssetTypeAndAssetIdOrderByTimestampDesc(assetType, assetId, page));
    }

    @GetMapping
    public PagedModel<AuditLog> getAllAuditLogs(
            Pageable page,
            @RequestParam(required = false) List<String> assetType,
            @RequestParam(required = false) List<String> assetId) {
        Page<AuditLog> auditLogs;

        if (assetType != null && !assetType.isEmpty() && assetId != null && !assetId.isEmpty()) {
            auditLogs = auditLogRepository.findByAssetTypeInAndAssetIdInOrderByTimestampDesc(assetType, assetId, page);
        } else if (assetId != null && !assetId.isEmpty()) {
            auditLogs = auditLogRepository.findByAssetIdInOrderByTimestampDesc(assetId, page);
        } else if (assetType != null && !assetType.isEmpty()) {
            auditLogs = auditLogRepository.findByAssetTypeInOrderByTimestampDesc(assetType, page);
        } else {
            auditLogs = auditLogRepository.findByOrderByTimestampDesc(page);
        }

        return new PagedModel<>(auditLogs);
    }
}
