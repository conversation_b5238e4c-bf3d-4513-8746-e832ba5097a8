package com.esource.transformprojectservice.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.esource.mongo.search.SearchRequest;
import com.esource.transformprojectservice.model.dto.request.ProjectItemStatePatchDTO;
import com.esource.transformprojectservice.model.dto.response.ActivityDTO;
import com.esource.transformprojectservice.model.dto.response.ProjectItemDTO;
import com.esource.transformprojectservice.model.dto.response.ProjectItemsHealthDTO;
import com.esource.transformprojectservice.model.dto.response.StageGateDTO;
import com.esource.transformprojectservice.service.ActivityService;
import com.esource.transformprojectservice.service.ProjectItemService;
import com.esource.transformprojectservice.service.ProjectItemStateService;
import com.esource.transformprojectservice.service.ProjectItemsHealthService;

@RestController
@RequestMapping("/v1/project-items")
public class ProjectItemController {

    @Autowired
    private ActivityService activityService;

    @Autowired
    private ProjectItemService projectItemService;

    @Autowired
    private ProjectItemStateService projectItemStateService;

    @Autowired
    private ProjectItemsHealthService projectItemsHealthService;

    @PatchMapping("/{projectItemId}")
    public ProjectItemDTO patchProjectItemState(
            @PathVariable String projectItemId, @RequestBody ProjectItemStatePatchDTO projectItemStatePatchDTO) {
        return projectItemService.updateProjectItemState(projectItemId, projectItemStatePatchDTO);
    }

    @PostMapping("/search")
    private PagedModel<ProjectItemDTO> searchProjectItems(@RequestBody SearchRequest searchRequest, Pageable pageable) {
        return new PagedModel<>(projectItemService.getItemPage(searchRequest, pageable));
    }

    @GetMapping("/{projectItemId}/gates")
    public List<StageGateDTO> getStagesForProjectItem(@PathVariable String projectItemId) {
        return activityService.getStageGatesForItem(projectItemId);
    }

    @GetMapping("/{projectItemId}/activities")
    public List<ActivityDTO> getActivitiesByProjectItemId(@PathVariable String projectItemId) {
        return activityService.getActivitiesByProjectItemId(projectItemId);
    }

    @GetMapping("/health")
    public ProjectItemsHealthDTO getProjectItemsHealth() {
        return projectItemsHealthService.getProjectItemsHealth();
    }
}
