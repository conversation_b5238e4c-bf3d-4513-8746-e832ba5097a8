package com.esource.transformprojectservice.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PagedModel;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.esource.transformprojectservice.model.dto.request.ProjectItemStatePatchDTO;
import com.esource.transformprojectservice.model.dto.response.ActivityDTO;
import com.esource.transformprojectservice.model.dto.response.ProjectItemDTO;
import com.esource.transformprojectservice.model.entity.Project;
import com.esource.transformprojectservice.service.ActivityService;
import com.esource.transformprojectservice.service.ProjectItemService;
import com.esource.transformprojectservice.service.ProjectItemStateService;
import com.esource.transformprojectservice.service.ProjectService;

@RestController
@RequestMapping("/v1/projects")
public class ProjectController {

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ProjectItemService projectItemService;

    @Autowired
    private ProjectItemStateService projectItemStateService;

    @Autowired
    private ActivityService activityService;

    @GetMapping
    public PagedModel<Project> getAllProjects(Pageable pageable) {
        return new PagedModel<>(projectService.getAllProjects(pageable));
    }

    @GetMapping("/{projectId}")
    public Project getProjectById(@PathVariable String projectId) {
        return projectService.getProjectById(projectId);
    }

    @GetMapping("/{projectId}/items")
    public List<ProjectItemDTO> getProjectItemsByProjectId(@PathVariable String projectId) {
        return projectItemService.getProjectItemsByProjectId(projectId);
    }

    @GetMapping("/{projectId}/items/{projectItemId}")
    public ProjectItemDTO getProjectItemById(@PathVariable String projectId, @PathVariable String projectItemId) {
        return projectItemService.getProjectItemById(projectId, projectItemId);
    }

    @PatchMapping("/{projectId}/items/{projectItemId}")
    public ProjectItemDTO patchProjectItemState(
            @PathVariable String projectId,
            @PathVariable String projectItemId,
            @RequestBody ProjectItemStatePatchDTO projectItemStatePatchDTO) {
        return projectItemService.updateProjectItemState(projectItemId, projectItemStatePatchDTO);
    }

    @GetMapping("/{projectId}/items/{projectItemId}/activities")
    public List<ActivityDTO> getActivitiesByProjectItemId(
            @PathVariable String projectId, @PathVariable String projectItemId) {
        return activityService.getActivitiesByProjectItemId(projectItemId);
    }
}
