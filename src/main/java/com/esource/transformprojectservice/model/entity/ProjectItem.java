package com.esource.transformprojectservice.model.entity;

import java.util.Date;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import com.esource.transformprojectservice.model.enumeration.Status;
import com.esource.transformprojectservice.model.enumeration.WorkType;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@Document("project_items")
@AllArgsConstructor
@NoArgsConstructor
public class ProjectItem {

    @Id
    private String id;

    private String projectId;

    private String type;

    private WorkType workType;

    private Status status;

    private String name;

    private String description;

    private String projectManager;

    private Date inServiceByDate;

    private Double totalEstimatedCost;

    private Double totalActualCost;

    private Double totalBudgetedCost;

    private Double totalForecastedCost;

    private Date startDate;

    private Date endDate;
}
