package com.esource.transformprojectservice.model.entity;

import java.util.List;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import com.esource.transformprojectservice.model.embedded.ProjectItemRisk;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
@Document("project_item_states")
public class ProjectItemState {

    @Id
    private String id;

    private String projectId;

    private List<ProjectItemRisk> risks;

    private List<String> flags;
}
