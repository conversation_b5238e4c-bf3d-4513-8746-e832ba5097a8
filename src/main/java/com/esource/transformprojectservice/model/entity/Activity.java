package com.esource.transformprojectservice.model.entity;

import java.util.Date;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import lombok.Data;

@Data
@Document("activities")
public class Activity {

    @Id
    private String id;

    private String projectItemId;

    private String activityId;

    private String name;

    private String status;

    private String gate;

    private Date plannedStartDate;
    private Date plannedEndDate;

    private Date actualStartDate;
    private Date actualEndDate;
}
