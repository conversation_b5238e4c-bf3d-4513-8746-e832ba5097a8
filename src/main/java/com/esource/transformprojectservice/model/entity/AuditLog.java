package com.esource.transformprojectservice.model.entity;

import java.util.List;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import com.esource.transformprojectservice.model.embedded.AuditFieldDiff;
import com.esource.transformprojectservice.model.entity.auditing.Auditable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.Singular;
import lombok.experimental.SuperBuilder;

@Data
@Document("audit_logs")
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AuditLog extends Auditable {

    @Id
    private String id;

    private String assetType;
    private String assetId;
    private String operation;

    private Object oldState;
    private Object newState;

    @Singular
    private List<AuditFieldDiff> diffs;
}
