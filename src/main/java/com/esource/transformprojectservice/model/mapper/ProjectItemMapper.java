package com.esource.transformprojectservice.model.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.esource.transformprojectservice.model.dto.response.ProjectItemDTO;
import com.esource.transformprojectservice.model.entity.ProjectItem;
import com.esource.transformprojectservice.model.entity.ProjectItemState;

@Mapper(componentModel = "spring", imports = java.util.Collections.class)
public interface ProjectItemMapper {

    @Mapping(
            target = "risks",
            expression = "java(state != null && state.getRisks() != null ? state.getRisks() : Collections.emptyList())")
    @Mapping(
            target = "flags",
            expression = "java(state != null && state.getFlags() != null ? state.getFlags() : Collections.emptyList())")
    @Mapping(target = "projectId", source = "item.projectId")
    @Mapping(target = "id", source = "item.id")
    ProjectItemDTO toDTO(ProjectItem item, ProjectItemState state);
}
