package com.esource.transformprojectservice.model.dto.response;

import java.util.Date;
import java.util.List;

import com.esource.transformprojectservice.model.embedded.ProjectItemRisk;
import com.esource.transformprojectservice.model.enumeration.Status;
import com.esource.transformprojectservice.model.enumeration.WorkType;

import lombok.Builder;

@Builder
public record ProjectItemDTO(
        String id,
        String projectId,
        String type,
        WorkType workType,
        Status status,
        String name,
        String description,
        String projectManager,
        Date inServiceByDate,
        Date startDate,
        Date endDate,
        Double totalEstimatedCost,
        Double totalActualCost,
        Double totalBudgetedCost,
        Double totalForecastedCost,
        List<ProjectItemRisk> risks,
        List<String> flags) {

    public ProjectItemDTO {
        risks = risks != null ? risks : List.of();
        flags = flags != null ? flags : List.of();
    }
}
