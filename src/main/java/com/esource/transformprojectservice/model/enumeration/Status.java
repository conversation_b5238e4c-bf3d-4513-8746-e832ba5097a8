package com.esource.transformprojectservice.model.enumeration;

import java.util.Set;
import java.util.stream.Stream;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum Status {
    APPROVED("Approved"),
    IN_SERVICE("In Service"),
    CANCELLED("Cancelled"),
    WORKING("Working"),
    COMPLETED("Completed");

    public static final Set<Status> ACTIVE_STATUSES = Set.of(APPROVED, WORKING, IN_SERVICE, COMPLETED);

    private final String label;

    Status(String label) {
        this.label = label;
    }

    @JsonValue
    public String getLabel() {
        return this.label;
    }

    @JsonCreator
    public static Status of(String source) {

        return Stream.of(Status.values())
                .filter(status -> status.label.equalsIgnoreCase(source))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Invalid Status: " + source));
    }
}
