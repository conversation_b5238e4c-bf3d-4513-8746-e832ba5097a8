package com.esource.transformprojectservice.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.esource.transformprojectservice.model.dto.response.ActivityDTO;
import com.esource.transformprojectservice.model.dto.response.StageGateDTO;
import com.esource.transformprojectservice.model.mapper.ActivityMapper;
import com.esource.transformprojectservice.repository.ActivityRepository;

@Service
public class ActivityService {

    @Autowired
    private ActivityRepository activityRepository;

    @Autowired
    private ActivityMapper activityMapper;

    public List<ActivityDTO> getActivitiesByProjectItemId(String projectItemId) {

        return activityRepository.findByProjectItemId(projectItemId).stream()
                .map(activityMapper::toDTO)
                .toList();
    }

    public List<StageGateDTO> getStageGatesForItem(String projectItemId) {
        return activityRepository.getStageGatesForItem(projectItemId);
    }
}
