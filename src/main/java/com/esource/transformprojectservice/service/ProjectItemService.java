package com.esource.transformprojectservice.service;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;

import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.LookupOperation;
import org.springframework.data.rest.webmvc.ResourceNotFoundException;
import org.springframework.stereotype.Service;

import com.esource.mongo.search.SearchRequest;
import com.esource.transformprojectservice.model.dto.request.ProjectItemStatePatchDTO;
import com.esource.transformprojectservice.model.dto.response.ProjectItemDTO;
import com.esource.transformprojectservice.model.embedded.AuditFieldDiff;
import com.esource.transformprojectservice.model.entity.AuditLog;
import com.esource.transformprojectservice.model.entity.ProjectItem;
import com.esource.transformprojectservice.model.entity.ProjectItemState;
import com.esource.transformprojectservice.model.mapper.ProjectItemMapper;
import com.esource.transformprojectservice.repository.AuditLogRepository;
import com.esource.transformprojectservice.repository.ProjectItemRepository;
import com.esource.transformprojectservice.repository.ProjectItemStateRepository;

import static com.esource.transformprojectservice.model.enumeration.AuditAssetType.PROJECT_ITEM;
import static com.esource.transformprojectservice.model.enumeration.AuditOperation.UPDATE;
import static com.esource.transformprojectservice.model.enumeration.Status.ACTIVE_STATUSES;
import static com.esource.transformprojectservice.model.enumeration.Status.CANCELLED;
import static java.util.Objects.requireNonNullElse;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toMap;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.count;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.limit;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.skip;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.sort;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.unwind;

@Service
public class ProjectItemService {

    @Autowired
    private ProjectItemRepository projectItemRepository;

    @Autowired
    private ProjectItemStateRepository projectItemStateRepository;

    @Autowired
    private AuditLogRepository auditLogRepository;

    @Autowired
    private ProjectItemMapper projectItemMapper;

    @Autowired
    private MongoTemplate mongoTemplate;

    private static final String[] PROJECT_ITEM_FIELD_NAMES = Arrays.stream(ProjectItem.class.getDeclaredFields())
            .map(Field::getName)
            .toArray(String[]::new);

    private static final List<AggregationOperation> BASE_AGGREGATION_OPERATIONS = List.of(
            LookupOperation.newLookup()
                    .from("project_item_states")
                    .localField("_id")
                    .foreignField("_id")
                    .as("state"),
            unwind("$state", true),
            project(PROJECT_ITEM_FIELD_NAMES)
                    .and("state.flags")
                    .as("flags")
                    .and("state.risks")
                    .as("risks"));

    public Long getActiveProjectCount() {
        return requireNonNullElse(projectItemRepository.countProjectIdsByStatusIn(ACTIVE_STATUSES), 0L);
    }

    public Long getCancelledProjectCount() {
        return requireNonNullElse(projectItemRepository.countProjectIdsByStatusIn(Set.of(CANCELLED)), 0L);
    }

    public Page<ProjectItemDTO> getItemPage(SearchRequest request, Pageable pageable) {

        long total = getTotalCount(request);

        if (total == 0) {
            return Page.empty(pageable);
        }

        var content = executePagedQuery(buildSearchAggregationOperations(request), pageable);

        return new PageImpl<>(content, pageable, total);
    }

    public List<ProjectItemDTO> getProjectItemsByProjectId(String projectId) {
        List<ProjectItem> projectItems = projectItemRepository.findByProjectId(projectId);
        List<ProjectItemState> projectItemStates = projectItemStateRepository.findByProjectId(projectId);

        Map<String, ProjectItemState> projectItemStateMap =
                projectItemStates.stream().collect(toMap(ProjectItemState::getId, Function.identity()));
        return projectItems.stream()
                .map(item -> projectItemMapper.toDTO(item, projectItemStateMap.get(item.getId())))
                .toList();
    }

    public ProjectItemDTO updateProjectItemState(String projectItemId, ProjectItemStatePatchDTO patchDTO) {
        if (patchDTO == null || (patchDTO.flags() == null && patchDTO.risks() == null)) {
            throw new IllegalArgumentException("Patch request must contain at least one field to update");
        }

        ProjectItem projectItem =
                projectItemRepository.findById(projectItemId).orElseThrow(ResourceNotFoundException::new);

        ProjectItemState state = projectItemStateRepository
                .findById(projectItemId)
                .orElse(ProjectItemState.builder()
                        .id(projectItemId)
                        .projectId(projectItem.getProjectId())
                        .build());

        var oldProjectItemDTO = projectItemMapper.toDTO(projectItem, state);

        var auditLogBuilder = AuditLog.builder()
                .assetType(PROJECT_ITEM.name())
                .assetId(projectItemId)
                .operation(UPDATE.name())
                .oldState(oldProjectItemDTO);

        if (patchDTO.risks() != null) {
            auditLogBuilder.diff(new AuditFieldDiff("risks", state.getRisks(), patchDTO.risks()));
            state.setRisks(patchDTO.risks());
        }
        if (patchDTO.flags() != null) {
            auditLogBuilder.diff(new AuditFieldDiff("flags", state.getFlags(), patchDTO.flags()));
            state.setFlags(patchDTO.flags());
        }

        state = projectItemStateRepository.save(state);

        var newProjectItemDTO = projectItemMapper.toDTO(projectItem, state);

        auditLogRepository.save(auditLogBuilder.newState(newProjectItemDTO).build());

        return newProjectItemDTO;
    }

    public ProjectItemDTO getProjectItemById(String projectId, String projectItemId) {
        var projectItem = projectItemRepository
                .findByIdAndProjectId(projectItemId, projectId)
                .orElseThrow(ResourceNotFoundException::new);

        var projectItemState =
                projectItemStateRepository.findById(projectItemId).orElse(null);

        return projectItemMapper.toDTO(projectItem, projectItemState);
    }

    private List<AggregationOperation> buildSearchAggregationOperations(SearchRequest request) {
        List<AggregationOperation> operations = new ArrayList<>(BASE_AGGREGATION_OPERATIONS);

        var matchOperation = request.toMatchOperation();
        if (matchOperation != null) {
            operations.add(matchOperation);
        }

        return operations;
    }

    private List<ProjectItemDTO> executePagedQuery(List<AggregationOperation> searchOperations, Pageable pageable) {
        addSortingIfPresent(searchOperations, pageable);
        addPagination(searchOperations, pageable);

        Aggregation aggregation = newAggregation(searchOperations);

        return mongoTemplate
                .aggregate(aggregation, ProjectItem.class, ProjectItemDTO.class)
                .getMappedResults();
    }

    private void addSortingIfPresent(List<AggregationOperation> operations, Pageable pageable) {
        if (!pageable.getSort().isUnsorted()) {
            operations.add(sort(pageable.getSort()));
        }
    }

    private void addPagination(List<AggregationOperation> operations, Pageable pageable) {
        operations.add(skip((long) pageable.getPageNumber() * pageable.getPageSize()));
        operations.add(limit(pageable.getPageSize()));
    }

    @Cacheable(value = "projectItemTotalCount", key = "#request.hashCode()")
    private long getTotalCount(SearchRequest request) {
        if (request.isEmptySearch()) {
            return mongoTemplate.estimatedCount(ProjectItem.class);
        }

        List<AggregationOperation> countOperations = buildSearchAggregationOperations(request);

        countOperations.add(count().as("totalCount"));

        Aggregation countAggregation = newAggregation(countOperations);

        return ofNullable(mongoTemplate
                        .aggregate(countAggregation, ProjectItem.class, Document.class)
                        .getUniqueMappedResult())
                .map(doc -> ((Number) doc.get("totalCount")).longValue())
                .orElse(0L);
    }
}
