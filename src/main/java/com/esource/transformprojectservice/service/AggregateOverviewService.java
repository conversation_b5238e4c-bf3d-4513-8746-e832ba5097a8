package com.esource.transformprojectservice.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.esource.transformprojectservice.model.dto.response.AggregateOverviewDTO;
import com.esource.transformprojectservice.model.dto.response.AggregateOverviewDTO.ProjectOverview;

@Service
public class AggregateOverviewService {

    @Autowired
    private ProjectService projectService;

    @Autowired
    private ProjectItemService projectItemService;

    public AggregateOverviewDTO generateAggregateOverview() {

        var projectOverview = ProjectOverview.builder()
                .totalCount(projectService.getTotalProjectCount())
                .activeCount(projectItemService.getActiveProjectCount())
                .cancelledCount(projectItemService.getCancelledProjectCount())
                .build();

        return AggregateOverviewDTO.builder().projectOverview(projectOverview).build();
    }
}
