package com.esource.transformprojectservice.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.rest.webmvc.ResourceNotFoundException;
import org.springframework.stereotype.Service;

import com.esource.transformprojectservice.model.entity.Project;
import com.esource.transformprojectservice.repository.ProjectRepository;

@Service
public class ProjectService {

    @Autowired
    private ProjectRepository projectRepository;

    public Project getProjectById(String projectId) {
        return projectRepository.findById(projectId).orElseThrow(ResourceNotFoundException::new);
    }

    public Page<Project> getAllProjects(Pageable pageable) {
        return projectRepository.findAll(pageable);
    }

    public Long getTotalProjectCount() {
        return projectRepository.count();
    }
}
