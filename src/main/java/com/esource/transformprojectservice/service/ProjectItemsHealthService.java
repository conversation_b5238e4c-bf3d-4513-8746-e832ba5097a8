package com.esource.transformprojectservice.service;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.format.TextStyle;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Random;

import org.springframework.stereotype.Service;

import com.esource.transformprojectservice.model.dto.response.ProjectItemsHealthDTO;
import com.esource.transformprojectservice.model.dto.response.ProjectItemsHealthDataDTO;
import com.esource.transformprojectservice.model.dto.response.ProjectItemsHealthMonthDTO;
import com.esource.transformprojectservice.model.dto.response.ProjectItemsHealthTypeDTO;

@Service
public class ProjectItemsHealthService {

    private static final String[] HEALTH_TYPES = {
        "Health", "Schedule", "Budget", "Design", "Land", "Materials", "Outage"
    };

    private final Random random = new Random();

    public ProjectItemsHealthDTO getProjectItemsHealth() {
        List<ProjectItemsHealthTypeDTO> data = new ArrayList<>();

        for (String type : HEALTH_TYPES) {
            data.add(ProjectItemsHealthTypeDTO.builder()
                    .type(type)
                    .months(generateMonthsData())
                    .build());
        }

        return ProjectItemsHealthDTO.builder().data(data).build();
    }

    private List<ProjectItemsHealthMonthDTO> generateMonthsData() {
        List<ProjectItemsHealthMonthDTO> months = new ArrayList<>();
        LocalDate currentDate = LocalDate.now();

        // Generate data for the last 12 months in descending order
        for (int i = 0; i < 12; i++) {
            LocalDate monthDate = currentDate.minusMonths(i);

            // Get timestamp for the first day of the month at UTC
            Instant instant = monthDate.withDayOfMonth(1).atStartOfDay().toInstant(ZoneOffset.UTC);

            months.add(ProjectItemsHealthMonthDTO.builder()
                    .month(monthDate.getMonth().getDisplayName(TextStyle.FULL, Locale.ENGLISH))
                    .year(String.valueOf(monthDate.getYear()))
                    .timestamp(instant.getEpochSecond())
                    .projectItems(generateRandomProjectItemsData())
                    .build());
        }

        return months;
    }

    private ProjectItemsHealthDataDTO generateRandomProjectItemsData() {
        return ProjectItemsHealthDataDTO.builder()
                .risk(random.nextInt(20) + 1) // Random between 1-20
                .monitoring(random.nextInt(25) + 1) // Random between 1-25
                .onTrack(random.nextInt(50) + 1) // Random between 1-50
                .build();
    }
}
