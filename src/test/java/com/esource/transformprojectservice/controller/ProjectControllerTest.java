package com.esource.transformprojectservice.controller;

import java.util.List;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.rest.webmvc.ResourceNotFoundException;
import org.springframework.http.MediaType;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;

import com.esource.transformprojectservice.model.dto.response.ProjectItemDTO;
import com.esource.transformprojectservice.model.entity.Project;
import com.esource.transformprojectservice.service.ActivityService;
import com.esource.transformprojectservice.service.ProjectItemService;
import com.esource.transformprojectservice.service.ProjectItemStateService;
import com.esource.transformprojectservice.service.ProjectService;

import static com.esource.transformprojectservice.model.enumeration.Status.IN_SERVICE;
import static com.esource.transformprojectservice.model.enumeration.Status.WORKING;
import static com.esource.transformprojectservice.model.enumeration.WorkType.LINE;
import static com.esource.transformprojectservice.model.enumeration.WorkType.SUB;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(controllers = ProjectController.class, excludeAutoConfiguration = SecurityAutoConfiguration.class)
public class ProjectControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockitoBean
    private ProjectService projectService;

    @MockitoBean
    private ProjectItemService projectItemService;

    @MockitoBean
    private ProjectItemStateService projectItemStateService;

    @MockitoBean
    private ActivityService activityService;

    @Test
    void getAllProjects_ShouldReturnPagedModel_WhenProjectsExist() throws Exception {
        Project project1 = Project.builder().id("1").name("Project 1").build();
        Project project2 = Project.builder().id("2").name("Project 2").build();
        Page<Project> projectPage = new PageImpl<>(List.of(project1, project2), PageRequest.of(0, 10), 2);

        when(projectService.getAllProjects(any(Pageable.class))).thenReturn(projectPage);

        mockMvc.perform(get("/v1/projects").param("page", "0").param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content.length()").value(2))
                .andExpect(jsonPath("$.page.totalElements").value(2));

        verify(projectService).getAllProjects(any(Pageable.class));
    }

    @Test
    void getProjectById_ShouldReturnProject_WhenProjectExists() throws Exception {
        String projectId = "project1";
        Project project = Project.builder()
                .id(projectId)
                .name("Test Project")
                .description("Test Description")
                .build();

        when(projectService.getProjectById(projectId)).thenReturn(project);

        mockMvc.perform(get("/v1/projects/{projectId}", projectId))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value(projectId))
                .andExpect(jsonPath("$.name").value("Test Project"))
                .andExpect(jsonPath("$.description").value("Test Description"));

        verify(projectService).getProjectById(projectId);
    }

    @Test
    void getProjectById_ShouldReturnNotFound_WhenProjectDoesNotExist() throws Exception {
        String projectId = "nonexistent";

        when(projectService.getProjectById(projectId)).thenThrow(new ResourceNotFoundException());

        mockMvc.perform(get("/v1/projects/{projectId}", projectId)).andExpect(status().isNotFound());

        verify(projectService).getProjectById(projectId);
    }

    @Test
    void getProjectItemsByProjectId_ShouldReturnProjectItems_WhenProjectItemsExist() throws Exception {
        String projectId = "project1";
        ProjectItemDTO projectItem1 = ProjectItemDTO.builder()
                .projectId(projectId)
                .name("item1")
                .description("description1")
                .workType(LINE)
                .status(IN_SERVICE)
                .build();

        ProjectItemDTO projectItem2 = ProjectItemDTO.builder()
                .projectId(projectId)
                .name("item2")
                .description("description2")
                .workType(SUB)
                .status(WORKING)
                .build();

        List<ProjectItemDTO> projectItems = List.of(projectItem1, projectItem2);

        when(projectItemService.getProjectItemsByProjectId(projectId)).thenReturn(projectItems);

        mockMvc.perform(get("/v1/projects/{projectId}/items", projectId))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(2))
                .andExpect(jsonPath("$[0].projectId").value(projectId))
                .andExpect(jsonPath("$[0].name").value("item1"))
                .andExpect(jsonPath("$[1].name").value("item2"));

        verify(projectItemService).getProjectItemsByProjectId(projectId);
    }

    @Test
    void getProjectItemsByProjectId_ShouldReturnEmptyList_WhenNoProjectItemsExist() throws Exception {
        String projectId = "nonexistent";

        when(projectItemService.getProjectItemsByProjectId(projectId)).thenReturn(List.of());

        mockMvc.perform(get("/v1/projects/{projectId}/items", projectId))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(0));

        verify(projectItemService).getProjectItemsByProjectId(projectId);
    }
}
