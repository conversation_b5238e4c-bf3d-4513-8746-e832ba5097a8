package com.esource.transformprojectservice.controller;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import com.esource.transformprojectservice.service.ActivityService;
import com.esource.transformprojectservice.service.ProjectItemService;
import com.esource.transformprojectservice.service.ProjectItemStateService;
import com.esource.transformprojectservice.service.ProjectItemsHealthService;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(ProjectItemController.class)
class ProjectItemsHealthControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ActivityService activityService;

    @MockBean
    private ProjectItemService projectItemService;

    @MockBean
    private ProjectItemStateService projectItemStateService;

    @MockBean
    private ProjectItemsHealthService projectItemsHealthService;

    @Test
    @WithMockUser
    void testGetProjectItemsHealth() throws Exception {
        mockMvc.perform(get("/v1/project-items/health")).andDo(print()).andExpect(status().isOk());
    }
}
