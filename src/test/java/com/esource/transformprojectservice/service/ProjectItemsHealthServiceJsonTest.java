package com.esource.transformprojectservice.service;

import org.junit.jupiter.api.Test;

import com.esource.transformprojectservice.model.dto.response.ProjectItemsHealthDTO;
import com.fasterxml.jackson.databind.ObjectMapper;

class ProjectItemsHealthServiceJsonTest {

    private final ProjectItemsHealthService service = new ProjectItemsHealthService();
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    void testJsonSerialization() throws Exception {
        ProjectItemsHealthDTO result = service.getProjectItemsHealth();

        String json = objectMapper.writeValueAsString(result);
        System.out.println("Generated JSON:");
        System.out.println(json);

        // Verify we can deserialize it back
        ProjectItemsHealthDTO deserialized = objectMapper.readValue(json, ProjectItemsHealthDTO.class);
        assert deserialized != null;
        assert deserialized.data().size() == 7;
    }
}
