package com.esource.transformprojectservice.service;

import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.esource.transformprojectservice.model.dto.response.ProjectItemDTO;
import com.esource.transformprojectservice.model.entity.ProjectItem;
import com.esource.transformprojectservice.model.mapper.ProjectItemMapper;
import com.esource.transformprojectservice.repository.ProjectItemRepository;
import com.esource.transformprojectservice.repository.ProjectItemStateRepository;

import static com.esource.transformprojectservice.model.enumeration.Status.*;
import static com.esource.transformprojectservice.model.enumeration.WorkType.LINE;
import static com.esource.transformprojectservice.model.enumeration.WorkType.SUB;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ProjectItemServiceTest {

    @Mock
    private ProjectItemRepository projectItemRepository;

    @Mock
    private ProjectItemStateRepository projectItemStateRepository;

    @Mock
    private ProjectItemMapper projectItemMapper;

    @InjectMocks
    private ProjectItemService projectItemService;

    @Test
    void getActiveProjectCount_ShouldReturnCount_WhenActiveProjectsExist() {
        Long expectedCount = 5L;

        when(projectItemRepository.countProjectIdsByStatusIn(ACTIVE_STATUSES)).thenReturn(expectedCount);

        Long actualCount = projectItemService.getActiveProjectCount();

        assertEquals(expectedCount, actualCount);
        verify(projectItemRepository).countProjectIdsByStatusIn(ACTIVE_STATUSES);
    }

    @Test
    void getActiveProjectCount_ShouldReturnZero_WhenRepositoryReturnsNull() {
        when(projectItemRepository.countProjectIdsByStatusIn(ACTIVE_STATUSES)).thenReturn(null);

        Long actualCount = projectItemService.getActiveProjectCount();

        assertEquals(0, actualCount);
        verify(projectItemRepository).countProjectIdsByStatusIn(ACTIVE_STATUSES);
    }

    @Test
    void getProjectItemsByProjectId_ShouldReturnProjectItems_WhenProjectItemsExist() {
        String projectId = "project1";
        ProjectItem projectItem1 = ProjectItem.builder()
                .projectId(projectId)
                .name("item1")
                .description("description1")
                .workType(LINE)
                .status(IN_SERVICE)
                .build();

        ProjectItem projectItem2 = ProjectItem.builder()
                .projectId(projectId)
                .name("item2")
                .description("description2")
                .workType(SUB)
                .status(WORKING)
                .build();

        List<ProjectItem> expectedItems = List.of(projectItem1, projectItem2);
        List<ProjectItemDTO> expectedItemDTOs =
                expectedItems.stream().map(ProjectItemServiceTest::toDTO).toList();

        when(projectItemRepository.findByProjectId(projectId)).thenReturn(expectedItems);
        when(projectItemStateRepository.findByProjectId(projectId)).thenReturn(List.of());
        when(projectItemMapper.toDTO(any(), any())).thenAnswer(invocation -> {
            ProjectItem item = invocation.getArgument(0);
            return toDTO(item);
        });

        List<ProjectItemDTO> actualItems = projectItemService.getProjectItemsByProjectId(projectId);

        assertEquals(expectedItemDTOs, actualItems);
        assertEquals(2, actualItems.size());
        verify(projectItemRepository).findByProjectId(projectId);
    }

    @Test
    void getProjectItemsByProjectId_ShouldReturnEmptyList_WhenNoProjectItemsExist() {
        String projectId = "nonexistent";

        when(projectItemRepository.findByProjectId(projectId)).thenReturn(List.of());
        when(projectItemStateRepository.findByProjectId(projectId)).thenReturn(List.of());

        var actualItems = projectItemService.getProjectItemsByProjectId(projectId);

        assertTrue(actualItems.isEmpty());
        verify(projectItemRepository).findByProjectId(projectId);
    }

    private static ProjectItemDTO toDTO(ProjectItem projectItem) {
        return ProjectItemDTO.builder()
                .id(projectItem.getId())
                .projectId(projectItem.getProjectId())
                .name(projectItem.getName())
                .description(projectItem.getDescription())
                .workType(projectItem.getWorkType())
                .status(projectItem.getStatus())
                .inServiceByDate(projectItem.getInServiceByDate())
                .startDate(projectItem.getStartDate())
                .endDate(projectItem.getEndDate())
                .build();
    }
}
