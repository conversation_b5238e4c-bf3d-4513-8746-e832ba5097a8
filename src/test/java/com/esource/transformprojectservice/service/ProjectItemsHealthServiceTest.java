package com.esource.transformprojectservice.service;

import org.junit.jupiter.api.Test;

import com.esource.transformprojectservice.model.dto.response.ProjectItemsHealthDTO;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

class ProjectItemsHealthServiceTest {

    private final ProjectItemsHealthService service = new ProjectItemsHealthService();

    @Test
    void testGetProjectItemsHealth() {
        ProjectItemsHealthDTO result = service.getProjectItemsHealth();

        assertNotNull(result);
        assertNotNull(result.data());
        assertEquals(7, result.data().size()); // 7 types: Health, Schedule, Budget, Design, Land, Materials, Outage

        // Check first type
        var firstType = result.data().get(0);
        assertEquals("Health", firstType.type());
        assertNotNull(firstType.months());
        assertEquals(12, firstType.months().size()); // 12 months

        // Check first month
        var firstMonth = firstType.months().get(0);
        assertNotNull(firstMonth.month());
        assertNotNull(firstMonth.year());
        assertNotNull(firstMonth.projectItems());

        // Check project items data
        var projectItems = firstMonth.projectItems();
        assertNotNull(projectItems.risk());
        assertNotNull(projectItems.monitoring());
        assertNotNull(projectItems.onTrack());
    }
}
