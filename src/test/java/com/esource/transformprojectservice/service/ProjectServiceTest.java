package com.esource.transformprojectservice.service;

import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.rest.webmvc.ResourceNotFoundException;

import com.esource.transformprojectservice.model.entity.Project;
import com.esource.transformprojectservice.repository.ProjectRepository;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ProjectServiceTest {

    @Mock
    private ProjectRepository projectRepository;

    @InjectMocks
    private ProjectService projectService;

    @Test
    void getProjectById_ShouldReturnProject_WhenProjectExists() {
        String projectId = "project1";
        Project expectedProject = Project.builder()
                .id(projectId)
                .name("Test Project")
                .description("Test Description")
                .build();

        when(projectRepository.findById(projectId)).thenReturn(Optional.of(expectedProject));

        Project actualProject = projectService.getProjectById(projectId);

        assertEquals(expectedProject, actualProject);
        verify(projectRepository).findById(projectId);
    }

    @Test
    void getProjectById_ShouldThrowResourceNotFoundException_WhenProjectDoesNotExist() {
        String projectId = "nonexistent";

        when(projectRepository.findById(projectId)).thenReturn(Optional.empty());

        assertThrows(ResourceNotFoundException.class, () -> projectService.getProjectById(projectId));
        verify(projectRepository).findById(projectId);
    }

    @Test
    void getAllProjects_ShouldReturnPageOfProjects_WhenProjectsExist() {
        Pageable pageable = PageRequest.of(0, 10);
        Project project1 = Project.builder().id("1").name("Project 1").build();
        Project project2 = Project.builder().id("2").name("Project 2").build();
        Page<Project> expectedPage = new PageImpl<>(List.of(project1, project2), pageable, 2);

        when(projectRepository.findAll(pageable)).thenReturn(expectedPage);

        Page<Project> actualPage = projectService.getAllProjects(pageable);

        assertEquals(expectedPage, actualPage);
        assertEquals(2, actualPage.getContent().size());
        verify(projectRepository).findAll(pageable);
    }

    @Test
    void getAllProjects_ShouldReturnEmptyPage_WhenNoProjectsExist() {
        Pageable pageable = PageRequest.of(0, 10);
        Page<Project> expectedPage = new PageImpl<>(List.of(), pageable, 0);

        when(projectRepository.findAll(pageable)).thenReturn(expectedPage);

        Page<Project> actualPage = projectService.getAllProjects(pageable);

        assertTrue(actualPage.getContent().isEmpty());
        verify(projectRepository).findAll(pageable);
    }
}
