package com.esource.transformprojectservice.repository;

import java.util.List;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.esource.transformprojectservice.model.entity.ProjectItem;
import com.esource.transformprojectservice.util.AbstractTestContainer;

import static com.esource.transformprojectservice.model.enumeration.Status.IN_SERVICE;
import static com.esource.transformprojectservice.model.enumeration.Status.WORKING;
import static com.esource.transformprojectservice.model.enumeration.WorkType.LINE;
import static com.esource.transformprojectservice.model.enumeration.WorkType.SUB;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class ProjectItemRepositoryTest extends AbstractTestContainer {

    @Autowired
    private ProjectItemRepository projectItemRepository;

    @AfterEach
    void tearDown() {
        projectItemRepository.deleteAll();
    }

    @Test
    void findByProjectID_ShouldReturnProjectItems_WhenProjectItemsExist() {
        ProjectItem projectItem1 = ProjectItem.builder()
                .projectId("project1")
                .name("item1")
                .description("description1")
                .workType(LINE)
                .status(IN_SERVICE)
                .build();

        ProjectItem projectItem2 = ProjectItem.builder()
                .projectId("project1")
                .name("item2")
                .description("description2")
                .workType(SUB)
                .status(WORKING)
                .build();

        projectItemRepository.saveAll(List.of(projectItem1, projectItem2));

        List<ProjectItem> actualItems = projectItemRepository.findByProjectId("project1");

        assertEquals(2, actualItems.size());
        assertTrue(actualItems.contains(projectItem1));
        assertTrue(actualItems.contains(projectItem2));
    }

    @Test
    void findByProjectID_ShouldReturnEmptyList_WhenNoProjectItemsExist() {
        List<ProjectItem> actualItems = projectItemRepository.findByProjectId("nonexistent");

        assertTrue(actualItems.isEmpty());
    }
}
