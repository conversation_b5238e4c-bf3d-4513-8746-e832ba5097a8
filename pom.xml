<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.5.3</version>
		<relativePath/>
		<!-- lookup parent from repository -->
	</parent>
	<groupId>com.esource</groupId>
	<artifactId>transform-project-service</artifactId>
	<version>0.0.1</version>
	<name>transform-project-service</name>
	<description>TransFORM Project Service</description>
	<properties>
		<java.version>21</java.version>
		<docker.image.repo>411985166407.dkr.ecr.us-east-1.amazonaws.com/esource/transform-project-service</docker.image.repo>
		<docker.image.tag>latest</docker.image.tag>
		<sonar.projectKey>trovedata_transform-project-service_fc647324-f3b0-49b5-8e20-0d18dcaacfb1</sonar.projectKey>
		<sonar.qualitygate.wait>true</sonar.qualitygate.wait>
		<spotless.version>2.44.2</spotless.version>
	</properties>
	<dependencies>
		<!-- Spring -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-mongodb</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-rest</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-actuator</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-actuator-autoconfigure</artifactId>
		</dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-cache</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.ben-manes.caffeine</groupId>
      <artifactId>caffeine</artifactId>
    </dependency>

		<dependency>
			<groupId>com.esource.security</groupId>
			<artifactId>security-adapter</artifactId>
			<version>3.7.0</version>
		</dependency>
		<dependency>
			<groupId>com.esource</groupId>
			<artifactId>mongo-search-utils</artifactId>
			<version>5.1.0</version>
		</dependency>

		<dependency>
		<groupId>org.mapstruct</groupId>
		<artifactId>mapstruct</artifactId>
		<version>1.6.3</version>
		</dependency>

		<!-- Dev Tools -->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>

		<!-- ApiDocs -->
		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
			<version>2.8.9</version>
		</dependency>

		<!-- CVE version overrides  -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.18.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.tomcat.embed</groupId>
			<artifactId>tomcat-embed-core</artifactId>
			<version>11.0.9</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-testcontainers</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.testcontainers</groupId>
			<artifactId>junit-jupiter</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.testcontainers</groupId>
			<artifactId>mongodb</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.diffplug.spotless</groupId>
			<artifactId>spotless-maven-plugin</artifactId>
			<version>${spotless.version}</version>
			<exclusions>
				<exclusion>
					<groupId>dev.equo.ide</groupId>
					<artifactId>solstice</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.eclipse.jgit</groupId>
					<artifactId>org.eclipse.jgit</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.eclipse.platform</groupId>
					<artifactId>org.eclipse.osgi</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<annotationProcessorPaths>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
							<version>${lombok.version}</version>
						</path>
						<path>
							<groupId>org.mapstruct</groupId>
							<artifactId>mapstruct-processor</artifactId>
							<version>1.6.3</version>
						</path>
					</annotationProcessorPaths>
					<source>${java.version}</source>
					<target>${java.version}</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<executions>
					<execution>
						<id>Build Docker Image</id>
						<phase>install</phase>
						<goals>
							<goal>build-image</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<layers>
						<enabled>true</enabled>
					</layers>
					<image>
						<name>${docker.image.repo}:${docker.image.tag}</name>
						<createdDate>now</createdDate>
						<securityOptions></securityOptions>
						<buildWorkspace>
							<bind>
								<source>/opt/atlassian/bitbucketci/agent/build/cache-${project.artifactId}.work</source>
							</bind>
						</buildWorkspace>
						<buildCache>
							<bind>
								<source>/opt/atlassian/bitbucketci/agent/build/cache-${project.artifactId}.build</source>
							</bind>
						</buildCache>
						<launchCache>
							<bind>
								<source>/opt/atlassian/bitbucketci/agent/build/cache-${project.artifactId}.launch</source>
							</bind>
						</launchCache>
					</image>
					<excludes>
						<exclude>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</exclude>
					</excludes>
				</configuration>
			</plugin>
			<plugin>
				<groupId>com.diffplug.spotless</groupId>
				<artifactId>spotless-maven-plugin</artifactId>
				<version>${spotless.version}</version>
				<configuration>
					<!-- optional: limit format enforcement to just the files changed by this
          feature branch-->
					<!-- <ratchetFrom>origin/main</ratchetFrom> -->
					<formats>
						<format>
							<includes>
								<include>.gitattributes</include>
								<include>.gitignore</include>
							</includes>
							<trimTrailingWhitespace />
							<endWithNewline />
							<indent>
								<spaces>true</spaces>
								<spacesPerTab>4</spacesPerTab>
							</indent>
						</format>
					</formats>
					<java>
						<palantirJavaFormat />
						<formatAnnotations />
						<removeUnusedImports />
						<trimTrailingWhitespace />
						<endWithNewline />
						<indent>
							<spaces>true</spaces>
							<spacesPerTab>4</spacesPerTab>
						</indent>
						<removeUnusedImports>
							<engine>google-java-format</engine>
						</removeUnusedImports>
						<importOrder>
							<order>java|javax,org,com,com.diffplug,,\#com.diffplug,\#</order>
							<semanticSort>false</semanticSort>
						</importOrder>
					</java>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>apply</goal>
						</goals>
						<phase>compile</phase>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

    <distributionManagement>
        <repository>
            <id>esource-int-artifacts-maven</id>
            <name>esource-int-artifacts-maven</name>
            <url>https://esource-int-artifacts-411985166407.d.codeartifact.us-east-1.amazonaws.com/maven/maven/</url>
        </repository>
    </distributionManagement>

</project>
